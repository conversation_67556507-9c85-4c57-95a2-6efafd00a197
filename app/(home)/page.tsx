import Image from 'next/image';
import Link from 'next/link';
import {
  MessageSquare,
  Users,
  Shield,
  Zap,
  Database,
  Monitor,
  Cpu,
  Lock,
  Globe,
  ExternalLink,
  CheckCircle,
  Star,
  ArrowRight,
  Sparkles,
  Menu
} from 'lucide-react';

// AI 服务商名称到文件名的映射
const AI_PROVIDER_LOGO_MAP: Record<string, string> = {
  'OpenAI': 'openai',
  '<PERSON>': 'claude',
  'Gemini': 'gemini',
  'DeepSeek': 'deepseek',
  'Qwen': 'qwen',
  'GLM': 'zhipu',        // GLM 对应 zhipu.svg
  'Moonshot': 'moonshot',
  'Doubao': 'doubao',    // 火山方舟（豆包）
  'Hunyuan': 'hunyuan',  // 腾讯混元
  'Baidu': 'qianfan',    // Baidu 对应 qianfan.svg（百度千帆）
  'SiliconFlow': 'siliconflow',
  'Minimax': 'minimax',
  'Grok': 'grok',
  'Ollama': 'ollama',
  'OpenRouter': 'openrouter',
  'Volcengine': 'volcengine'  // 火山引擎
};

// AI 服务商 Logo 组件 - SSR 友好版本，支持可靠的降级机制
function AIProviderLogo({ name, className = "" }: { name: string; className?: string }) {
  // 获取对应的文件名，如果没有映射或为空字符串，则使用默认占位
  const logoFileName = AI_PROVIDER_LOGO_MAP[name];
  const hasLogo = logoFileName && logoFileName.trim() !== '';

  // 如果有对应的 Logo 文件，使用 SVG 格式；否则使用默认 AI Logo
  const logoPath = hasLogo ? `/images/logo/${logoFileName}.svg` : '/images/logo/default-ai.svg';

  return (
    <div className={`flex items-center justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm hover:shadow-md transition-shadow min-h-[80px] ${className}`}>
      <div className="flex items-center justify-center w-full h-full relative">
        <div className="ai-provider-logo-container">
          <Image
            src={logoPath}
            alt={`${name} Logo`}
            width={40}
            height={40}
            className="object-contain max-w-full max-h-full"
            // 添加优先级和加载优化
            priority={false}
            loading="lazy"
          />
          {/* 如果没有对应的 Logo 文件，显示服务商名称作为额外标识 */}
          {!hasLogo && (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-medium text-gray-600 text-center mt-12 bg-white/80 px-2 py-1 rounded">
                {name}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Header 组件 - 使用纯 CSS 实现移动端菜单
function Header() {
  const navigation = [
    { name: '功能特性', href: '#features' },
    { name: '部署方案', href: '#deployment' },
    { name: '在线演示', href: 'https://chat.yotuku.cn/', external: true },
    { name: '查看文档', href: '/docs' },
    { name: 'GitHub', href: 'https://github.com/HiveNexus/HiveChat', external: true },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/85 backdrop-blur-xl border-b border-white/20 shadow-lg shadow-black/5">
      {/* Mobile menu toggle checkbox - 放在最外层 */}
      <input type="checkbox" id="mobile-menu-toggle" className="hidden peer" />

      <nav className="mx-auto max-w-7xl px-6 lg:px-8" aria-label="Top">
        <div className="flex w-full items-center justify-between py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <Image src="/icon.png" alt="HiveChatLogo" width={32} height={32} className="rounded-lg" />
              <Image src="/images/hivechat.svg" alt="HiveChat" width={110} height={28} />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                target={item.external ? '_blank' : undefined}
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200"
              >
                {item.name}
                {item.external && <ExternalLink className="ml-1 h-3 w-3 inline" />}
              </Link>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <label
              htmlFor="mobile-menu-toggle"
              className="rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200 cursor-pointer inline-flex items-center justify-center"
            >
              <span className="sr-only">打开菜单</span>
              <Menu className="h-6 w-6" aria-hidden="true" />
            </label>
          </div>
        </div>

        {/* Mobile Navigation - 使用 peer 选择器控制显示/隐藏 */}
        <div className="md:hidden hidden peer-checked:block bg-white/90 backdrop-blur-xl">
          <div className="space-y-1 pb-3 pt-2 border-t border-white/30">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                target={item.external ? '_blank' : undefined}
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200"
              >
                {item.name}
                {item.external && <ExternalLink className="ml-1 h-3 w-3 inline" />}
              </Link>
            ))}
          </div>
        </div>
      </nav>
    </header>
  );
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <section className="relative px-6 lg:px-8 pt-32 pb-32">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <div className='flex flex-row items-center justify-center'>
              <Image src="/icon.png" alt="HiveChat" width={60} height={60} className="rounded-2xl" />
              <Image src="/images/hivechat.svg" alt="HiveChat" className='ml-4' width={190} height={28} />
            </div>
            <p className="mt-6 text-xl leading-8 text-gray-600 max-w-3xl mx-auto">
              为中小团队设计的 AI 聊天应用，支持多用户模式、权限管理、MCP 协议，以及所有主流 AI 服务商接入
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="https://chat.yotuku.cn/"
                target="_blank"
                className="rounded-md bg-blue-600 px-6 py-2 text-lg font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors"
              >
                立即体验 <ExternalLink className="ml-2 h-5 w-5 inline" />
              </Link>
              <Link
                href="/docs"
                className="text-lg font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors"
              >
                查看文档 <ArrowRight className="ml-2 h-5 w-5 inline" />
              </Link>
            </div>

            {/* 特性标签 */}
            <div className="mt-12 flex flex-wrap justify-center gap-3">
              {['开源免费', '私有部署', '多用户管理', 'MCP 支持', '15+ AI 模型'].map((tag) => (
                <span key={tag} className="inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-800">
                  <Sparkles className="mr-1 h-4 w-4" />
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* AI Models Section */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              支持所有主流 AI 服务商
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              一站式接入全球顶级 AI 服务商，满足不同场景需求
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-5xl grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-5">
            {[
              'OpenAI', 'Claude', 'Gemini', 'DeepSeek', 'Qwen',
              'Moonshot', 'Minimax', 'Doubao', 'Hunyuan', 'Baidu',
              'SiliconFlow', 'Grok', 'Ollama', 'OpenRouter', 'Volcengine'
            ].map((model) => (
              <AIProviderLogo key={model} name={model} />
            ))}
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              核心功能特性
            </h2>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-8 lg:max-w-none lg:grid-cols-3">
              {[
                {
                  icon: Users,
                  title: '多用户模式',
                  desc: '支持团队协作，用户分组管理，权限精细控制'
                },
                {
                  icon: Shield,
                  title: '权限管理',
                  desc: '灵活的用户权限体系，Token 限额控制，安全可靠'
                },
                {
                  icon: Zap,
                  title: 'MCP 协议支持',
                  desc: '支持 Model Context Protocol，扩展 AI 能力边界'
                },
              ].map((feature) => (
                <div key={feature.title} className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <feature.icon className="h-5 w-5 flex-none text-blue-600" aria-hidden="true" />
                    {feature.title}
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">{feature.desc}</p>
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              丰富的功能特性
            </h2>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-8 lg:max-w-none lg:grid-cols-3">
              {[
                { icon: MessageSquare, title: '思维链', desc: '可视化展示 AI 思考过程' },
                { icon: Database, title: 'LaTeX & Markdown', desc: '完美渲染数学公式和文档' },
                { icon: Monitor, title: '图像理解', desc: '支持图片上传和多模态对话' },
                { icon: Cpu, title: 'AI 智能体', desc: '自定义 AI 助手和工作流' },
                { icon: Lock, title: '云端存储', desc: '安全可靠的数据存储方案' },
                { icon: Globe, title: '企业登录集成', desc: '支持邮箱、第三方登录方式' },
              ].map((feature) => (
                <div key={feature.title} className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <feature.icon className="h-5 w-5 flex-none text-blue-600" aria-hidden="true" />
                    {feature.title}
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">{feature.desc}</p>
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Deployment Options */}
      <section id="deployment" className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              灵活的部署方式
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              支持多种部署方案，满足不同团队需求
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {[
                {
                  title: '本地部署',
                  description: '完全私有化部署，数据安全可控',
                  features: ['数据完全私有', '自定义配置', '无外网依赖'],
                  icon: Monitor,
                },
                {
                  title: 'Docker 部署',
                  description: '一键容器化部署，简单快捷',
                  features: ['快速启动', '环境隔离', '易于维护'],
                  icon: Database,
                },
                {
                  title: 'Vercel 部署',
                  description: '云端 SaaS 服务，开箱即用',
                  features: ['零运维', '自动扩容', '全球加速'],
                  icon: Globe,
                },
              ].map((option) => (
                <div key={option.title} className="rounded-2xl bg-white p-8 shadow-sm ring-1 ring-gray-200">
                  <option.icon className="h-8 w-8 text-blue-600" />
                  <h3 className="mt-4 text-lg font-semibold text-gray-900">{option.title}</h3>
                  <p className="mt-2 text-sm text-gray-600">{option.description}</p>
                  <ul className="mt-4 space-y-2">
                    {option.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Demo & GitHub */}
      <section id="demo" className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              立即开始体验
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              在线演示或下载源码，快速上手 HiveChat
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-8 lg:grid-cols-2">
            {/* 在线演示 */}
            <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
              <div className="flex items-center">
                <ExternalLink className="h-8 w-8 text-blue-600" />
                <h3 className="ml-3 text-xl font-semibold text-gray-900">在线演示</h3>
              </div>
              <p className="mt-4 text-gray-600">
                体验完整功能，无需安装
              </p>
              <div className="mt-6 space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900">用户端演示</p>
                  <Link
                    href="https://chat.yotuku.cn/"
                    target="_blank"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    https://chat.yotuku.cn/
                  </Link>
                  <p className="text-xs text-gray-500">可自行注册账号体验</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">管理员端演示</p>
                  <Link
                    href="https://hivechat-demo.vercel.app/"
                    target="_blank"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    https://hivechat-demo.vercel.app/
                  </Link>
                  <p className="text-xs text-gray-500">Email: <EMAIL> / Password: helloHivechat</p>
                </div>
              </div>
            </div>

            {/* GitHub 仓库 */}
            <div className="rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 p-8">
              <div className="flex items-center">
                <ExternalLink className="h-8 w-8 text-gray-900" />
                <h3 className="ml-3 text-xl font-semibold text-gray-900">开源仓库</h3>
              </div>
              <p className="mt-4 text-gray-600">
                获取源码，自由定制和部署
              </p>
              <div className="mt-6">
                <Link
                  href="https://github.com/HiveNexus/HiveChat"
                  target="_blank"
                  className="inline-flex items-center rounded-md bg-gray-900 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 transition-colors"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  查看源码
                </Link>
                <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Star className="mr-1 h-4 w-4" />
                    开源免费
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="mr-1 h-4 w-4" />
                    MIT 协议
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600">
        <div className="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              准备好开始了吗？
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
              立即体验 HiveChat，为您的团队打造专属的 AI 聊天平台
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="https://chat.yotuku.cn/"
                target="_blank"
                className="rounded-md bg-white px-6 py-3 text-lg font-semibold text-blue-600 shadow-sm hover:bg-blue-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors"
              >
                立即体验
              </Link>
              <Link
                href="/docs"
                className="text-lg font-semibold leading-6 text-white hover:text-blue-100 transition-colors"
              >
                查看文档 <ArrowRight className="ml-2 h-5 w-5 inline" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
